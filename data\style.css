body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif; background-color: #f4f7f6; color: #333; margin: 0; padding: 20px; }
.container { max-width: 800px; margin: auto; }
h1 { color: #1a237e; text-align: center; }
.card { background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); padding: 25px; margin-bottom: 20px; }
.color-display { display: flex; justify-content: space-around; align-items: flex-start; flex-wrap: wrap; gap: 20px; }
.color-card { flex: 1; min-width: 250px; text-align: center; }
.color-card h2 { margin-top: 0; color: #3949ab; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; }
.swatch { width: 100%; height: 120px; border-radius: 6px; border: 1px solid #ccc; background-color: #f0f0f0; margin-bottom: 15px; transition: background-color 0.5s ease; }
.details p { margin: 8px 0; font-size: 1.1em; }
.stats { margin-top: 15px; display: flex; justify-content: space-between; align-items: flex-start; gap: 20px; flex-wrap: wrap; }
.capture-display { flex: 1; min-width: 250px; text-align: center; }
.capture-display h3 { margin-top: 0; color: #3949ab; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; }
.capture-details p { margin: 8px 0; font-size: 1.1em; }
.sample-info { flex: 1; min-width: 200px; text-align: center; }
.sample-info p { font-size: 1.2em; font-weight: bold; margin: 5px 0; }

/* Samples Button Styling */
.samples-button {
    background-color: #4CAF50 !important;
    color: white !important;
    padding: 12px 20px !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 1em !important;
    font-weight: bold !important;
    margin-top: 10px !important;
    display: inline-block !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    transition: all 0.3s ease !important;
}
.samples-button:hover {
    background-color: #45a049 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
}

/* Save Notification */
.save-notification {
    background-color: #2e7d32;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    margin-top: 8px;
    font-weight: bold;
    animation: slideIn 0.3s ease-out;
}
.save-icon {
    font-size: 1.2em;
    margin-right: 5px;
}
@keyframes slideIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
#confidence.high { color: #2e7d32; }
#confidence.medium { color: #f57f17; }
#confidence.low { color: #c62828; }
.collapsible-header { cursor: pointer; padding: 12px; background-color: #e8eaf6; border-radius: 8px; font-weight: bold; color: #1a237e; margin-top: 10px; user-select: none; }
.collapsible-header::after { content: '\25BC'; float: right; transition: transform 0.2s; }
.collapsible-header.active::after { transform: rotate(180deg); }
.collapsible-content { padding: 0 15px; max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background-color: #fafafa; border-radius: 0 0 8px 8px; }
.live-data-grid, .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; padding: 15px 0; }
label { font-weight: bold; margin-bottom: 5px; display: block; }
input, select { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; }
.form-actions { display: flex; justify-content: flex-end; align-items: center; margin-top: 15px; gap: 15px; }
button { background-color: #3949ab; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 1em; }
button:hover { background-color: #1a237e; }
#save-status { text-align: right; color: green; font-weight: bold; height: 1em; }
.control-buttons { display: flex; gap: 10px; justify-content: center; margin-bottom: 20px; }
#led-button.on { background-color: #2e7d32; }
#led-button.off { background-color: #c62828; }
#scan-button.stop { background-color: #c62828; }
#scan-status { text-align: center; color: #3949ab; font-weight: bold; margin-top: 10px; }
footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
.footer-signature { font-style: italic; font-size: 0.85em; color: #999; }
.setting-description { font-size: 0.9em; color: #666; margin-top: 3px; margin-bottom: 10px; }
.setting-group { border-left: 3px solid #3949ab; padding-left: 15px; margin-bottom: 20px; }
.setting-group h3 { color: #3949ab; margin-top: 0; }
.preview-container { display: flex; gap: 20px; margin-top: 15px; }
.preview-box { flex: 1; text-align: center; }
.preview-swatch { width: 100%; height: 80px; border-radius: 6px; border: 1px solid #ccc; margin-bottom: 10px; }
.preview-data { font-size: 0.9em; }
.tabs { display: flex; margin-bottom: 15px; border-bottom: 1px solid #ddd; }
.tab { padding: 10px 20px; cursor: pointer; border-radius: 5px 5px 0 0; }
.tab.active { background-color: #e8eaf6; border: 1px solid #ddd; border-bottom: none; font-weight: bold; }
.tab-content { display: none; }
.tab-content.active { display: block; }
.recommended-values { background-color: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 0.9em; }
.recommended-values h4 { margin-top: 0; margin-bottom: 5px; color: #3949ab; }
.recommended-values ul { margin: 0; padding-left: 20px; }
.error-message { color: #c62828; font-size: 0.9em; margin-top: 5px; display: none; }
.nav-buttons { display: flex; justify-content: space-between; margin-top: 20px; }
.nav-buttons button { background-color: #757575; }
.nav-buttons button:hover { background-color: #616161; }

/* Modal Styles */
.modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5); }
.modal-content { background-color: #fefefe; margin: 5% auto; padding: 0; border-radius: 12px; width: 90%; max-width: 900px; max-height: 85vh; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.modal-header { background-color: #3949ab; color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
.modal-header h2 { margin: 0; }
.close { color: white; font-size: 28px; font-weight: bold; cursor: pointer; }
.close:hover { color: #ddd; }
.modal-body { padding: 20px; max-height: 60vh; overflow-y: auto; }
.modal-footer { background-color: #f5f5f5; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }

/* Sample Grid Styles */
.samples-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; }
.sample-thumbnail { background-color: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: pointer; transition: transform 0.2s, box-shadow 0.2s; overflow: hidden; }
.sample-thumbnail:hover { transform: translateY(-2px); box-shadow: 0 4px 16px rgba(0,0,0,0.2); }
.sample-color { width: 100%; height: 80px; }
.sample-info { padding: 10px; }
.sample-number { font-weight: bold; color: #2196f3; font-size: 0.85em; margin-bottom: 3px; }
.sample-rgb { font-weight: bold; font-size: 0.9em; margin-bottom: 5px; }
.sample-match { font-size: 0.8em; color: #666; margin-bottom: 2px; font-weight: 500; }
.sample-code { font-size: 0.75em; color: #888; font-family: monospace; margin-bottom: 3px; }
.sample-confidence { font-size: 0.8em; color: #888; }

/* Sample Detail Styles */
.sample-detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
.detail-color-section, .detail-match-section { text-align: center; }
.detail-sensor-section { grid-column: 1 / -1; }
.detail-color-swatch, .detail-match-swatch { width: 100%; height: 100px; border-radius: 8px; border: 1px solid #ccc; margin-bottom: 15px; }
.detail-color-info, .detail-match-info, .detail-sensor-info { text-align: left; }
.detail-color-info p, .detail-match-info p, .detail-sensor-info p { margin: 8px 0; }

/* Loading and Empty States */
.loading { text-align: center; padding: 40px; color: #666; }
.empty-state { text-align: center; padding: 40px; color: #666; }
.empty-state p { margin: 10px 0; }

/* Countdown Display */
.countdown-display {
    background: linear-gradient(135deg, #3949ab, #5c6bc0);
    color: white;
    padding: 15px 20px;
    border-radius: 12px;
    margin: 15px 0;
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(57, 73, 171, 0.3);
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
    from { transform: scale(1); }
    to { transform: scale(1.02); }
}

.countdown-display.preparation {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.countdown-display.scanning {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.countdown-display.processing {
    background: linear-gradient(135deg, #2196f3, #42a5f5);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

/* Button Variants */
button.secondary { background-color: #757575; }
button.secondary:hover { background-color: #616161; }
button.danger { background-color: #c62828; }
button.danger:hover { background-color: #b71c1c; }

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content { width: 95%; margin: 2% auto; }
    .sample-detail-grid { grid-template-columns: 1fr; }
    .samples-grid { grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); }
}
