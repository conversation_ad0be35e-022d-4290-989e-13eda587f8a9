/*
 * ESP32-S3 Color Matching Device - Working Version with WiFi
 * 
 * A working version that compiles successfully and provides core functionality
 * with proper WiFi connectivity to "Wifi 6" network.
 * 
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - WiFi connectivity using ESP32 built-in WiFi
 * - Simple color matching with built-in color database
 * - LED status indicators
 * - Serial output for debugging and data display
 * - Basic error handling and sensor health checks
 * 
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - Status LED: GPIO 5
 * - Illumination LED: GPIO 4  
 * - Sensor Interrupt: GPIO 21
 * 
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include "DFRobot_TCS3430.h"  // Use local library
#include <ArduinoJson.h>
#include <math.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <Adafruit_NeoPixel.h>
#include <Preferences.h>

// ------------------------------------------------------------------------------------
// Configuration Constants
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define ILLUMINATION_LED_PIN 5  // White LED for scanning illumination
#define RGB_LED_PIN          18 // ProS3 onboard RGB LED (WS2812)
#define RGB_POWER_PIN        17 // ProS3 LDO2 power control for RGB LED
#define SENSOR_INTERRUPT_PIN 21
#define READING_INTERVAL_MS  500
#define MAX_COLOR_NAME_LEN   35
#define MAX_DULUX_COLORS     1500

// Network Configuration - Enhanced WiFi Diagnostics
const char* WIFI_SSID = "Wifi 6";
const char* WIFI_PASSWORD = "Scrofani1985";

// Try DHCP first, then fallback to static IP if needed
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);

// WiFi connection settings for better compatibility
const int WIFI_CONNECT_TIMEOUT = 20000;  // 20 seconds timeout
const int WIFI_RETRY_DELAY = 500;        // 500ms between retries

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
DFRobot_TCS3430 colorSensor;
Adafruit_NeoPixel rgbLED(1, RGB_LED_PIN, NEO_GRB + NEO_KHZ800); // ProS3 uses NEO_GRB format
unsigned long lastReading = 0;
unsigned long lastRGBUpdate = 0;
int rgbColorWheel = 0;
bool illuminationState = false;
bool manualLEDToggle = false;  // Manual LED toggle state (independent of scanning)
bool sensorInitialized = false;
bool wifiConnected = false;

// ------------------------------------------------------------------------------------
// Settings Structure for Precision Color Measurement
// ------------------------------------------------------------------------------------
struct ColorSettings {
    // Sensor Configuration
    uint8_t integrationTime;    // Integration time (0x00-0xFF, default 0x40 = ~100ms)
    uint8_t alsGain;           // ALS Gain (0=1x, 1=4x, 2=16x, 3=64x, default 1=4x)

    // Color Processing
    float irCompensation;      // IR compensation factor (default 0.03)
    uint16_t srgbNormalization; // sRGB normalization factor (default 15000)
    bool adaptiveScaling;      // Adaptive scaling enabled (default true)

    // Calibration flags
    bool isCalibrated;         // Overall calibration status
    bool hasWhiteBalance;      // White balance calibration status
    bool hasDarkCalibration;   // Dark calibration status
};

// Default settings for optimal color measurement
const ColorSettings DEFAULT_SETTINGS = {
    .integrationTime = 0x40,    // ~100ms for good accuracy
    .alsGain = 1,              // 4x gain for balanced sensitivity
    .irCompensation = 0.03f,   // Minimal IR compensation
    .srgbNormalization = 15000, // Standard normalization
    .adaptiveScaling = true,   // Enable adaptive scaling
    .isCalibrated = false,
    .hasWhiteBalance = false,
    .hasDarkCalibration = false
};

// Current settings (loaded from EEPROM or defaults)
ColorSettings currentSettings = DEFAULT_SETTINGS;

// Preferences object for EEPROM storage
Preferences preferences;

// ------------------------------------------------------------------------------------
// Precision Color Data Structure
// ------------------------------------------------------------------------------------
struct PrecisionColorData {
    uint16_t x, y, z;           // Raw XYZ tristimulus values
    uint16_t ir1, ir2;          // IR values
    float calibratedX, calibratedY, calibratedZ; // Calibrated XYZ values
    uint8_t r, g, b;            // RGB values (0-255)
    char hexColor[8];           // Hex color string
    float confidence;           // Measurement confidence (0-100%)
    bool isSaturated;           // Saturation warning
    bool isCalibrated;          // Using calibrated values
};

PrecisionColorData currentColor = {0};

// ------------------------------------------------------------------------------------
// Sample Storage Structure for Dual-Display System
// ------------------------------------------------------------------------------------
#define MAX_STORED_SAMPLES 30

struct ColorSample {
    // Measured color data
    uint8_t r, g, b;            // RGB values (0-255)
    char hexColor[8];           // Hex color string

    // Enhanced Dulux match data
    char duluxName[MAX_COLOR_NAME_LEN]; // Matched Dulux color name
    char duluxCode[16];         // Dulux paint code (e.g., "P01C1W")
    char duluxLRV[8];           // Light Reflectance Value (e.g., "85.20")
    char duluxID[16];           // Dulux ID (e.g., "251003")
    uint8_t duluxR, duluxG, duluxB;     // Dulux match RGB
    float deltaE;               // Color difference (Delta E)

    // Raw sensor data for reference
    uint16_t rawX, rawY, rawZ;  // Raw XYZ values
    uint16_t rawIR1, rawIR2;    // IR values
    float confidence;           // Measurement confidence

    // Sample metadata
    unsigned long timestamp;    // When sample was captured
    int sampleNumber;           // Sequential sample number
    bool isValid;              // Sample slot is occupied
};

// Circular buffer for stored samples
ColorSample storedSamples[MAX_STORED_SAMPLES];
int currentSampleIndex = 0;    // Next slot to write to
int totalSamples = 0;          // Total samples stored (max 30)

// Current frozen sample (displayed on right side)
ColorSample frozenSample = {0};
bool hasFrozenSample = false;

// Enhanced Dulux color database structure
struct ReferenceColor {
    char name[MAX_COLOR_NAME_LEN];
    char code[16];              // Dulux paint code (e.g., "P01C1W")
    char lrv[8];                // Light Reflectance Value (e.g., "85.20")
    char id[16];                // Dulux ID (e.g., "251003")
    uint8_t r, g, b;
};

// Use PSRAM for large color database to avoid heap memory issues
ReferenceColor* colorDatabase = nullptr;
int COLOR_DATABASE_SIZE = 0;

// Fallback colors if dulux.json fails to load
const ReferenceColor fallbackColors[] = {
    {"White", "FALLBACK", "90.0", "000001", 255, 255, 255},
    {"Black", "FALLBACK", "5.0", "000002", 0, 0, 0},
    {"Red", "FALLBACK", "25.0", "000003", 255, 0, 0},
    {"Green", "FALLBACK", "35.0", "000004", 0, 255, 0},
    {"Blue", "FALLBACK", "15.0", "000005", 0, 0, 255},
    {"Yellow", "FALLBACK", "80.0", "000006", 255, 255, 0},
    {"Cyan", "FALLBACK", "70.0", "000007", 0, 255, 255},
    {"Magenta", "FALLBACK", "30.0", "000008", 255, 0, 255},
    {"Orange", "FALLBACK", "45.0", "000009", 255, 165, 0},
    {"Purple", "FALLBACK", "20.0", "000010", 128, 0, 128},
    {"Brown", "FALLBACK", "18.0", "000011", 165, 42, 42},
    {"Gray", "FALLBACK", "40.0", "000012", 128, 128, 128},
    {"Pink", "FALLBACK", "60.0", "000013", 255, 192, 203},
    {"Lime", "FALLBACK", "75.0", "000014", 0, 255, 0},
    {"Navy", "FALLBACK", "10.0", "000015", 0, 0, 128}
};

const int FALLBACK_COLOR_COUNT = sizeof(fallbackColors) / sizeof(fallbackColors[0]);

// ------------------------------------------------------------------------------------
// Web Server Variables
// ------------------------------------------------------------------------------------
WebServer server(80);
bool isScanning = false;
unsigned long lastWebUpdate = 0;

// ------------------------------------------------------------------------------------
// Automatic Scanning System Variables
// ------------------------------------------------------------------------------------
enum ScanPhase {
    SCAN_IDLE,
    SCAN_PREPARATION,
    SCAN_ACTIVE,
    SCAN_PROCESSING
};

ScanPhase currentScanPhase = SCAN_IDLE;
unsigned long scanPhaseStartTime = 0;
unsigned long lastCountdownUpdate = 0;
unsigned long lastSampleTime = 0;
int preparationCountdown = 3;
int scanningCountdown = 5;

// Statistical sampling for improved accuracy
#define MAX_SCAN_SAMPLES 25
struct ColorSampleData {
    uint8_t r, g, b;
    uint16_t x, y, z;
    float confidence;
    bool isValid;
};

ColorSampleData scanSamples[MAX_SCAN_SAMPLES];
int currentScanSampleIndex = 0;
int totalValidSamples = 0;

// ------------------------------------------------------------------------------------
// Statistical Processing Functions for Automatic Scanning
// ------------------------------------------------------------------------------------

// Forward declarations
void processAndSaveStatisticalResult();
void handleAutomaticScanningStateMachine();

void initializeScanSamples() {
    currentScanSampleIndex = 0;
    totalValidSamples = 0;
    for (int i = 0; i < MAX_SCAN_SAMPLES; i++) {
        scanSamples[i].isValid = false;
    }
}

void addScanSample(const PrecisionColorData& colorData) {
    if (currentScanSampleIndex < MAX_SCAN_SAMPLES) {
        scanSamples[currentScanSampleIndex].r = colorData.r;
        scanSamples[currentScanSampleIndex].g = colorData.g;
        scanSamples[currentScanSampleIndex].b = colorData.b;
        scanSamples[currentScanSampleIndex].x = colorData.x;
        scanSamples[currentScanSampleIndex].y = colorData.y;
        scanSamples[currentScanSampleIndex].z = colorData.z;
        scanSamples[currentScanSampleIndex].confidence = colorData.confidence;
        scanSamples[currentScanSampleIndex].isValid = true;

        currentScanSampleIndex++;
        totalValidSamples++;

        Serial.printf("Sample %d: RGB(%d,%d,%d) Confidence=%.1f%%\n",
                     currentScanSampleIndex, colorData.r, colorData.g, colorData.b, colorData.confidence);
    }
}

float calculateMedian(uint8_t values[], int count) {
    // Simple bubble sort for small arrays
    for (int i = 0; i < count - 1; i++) {
        for (int j = 0; j < count - i - 1; j++) {
            if (values[j] > values[j + 1]) {
                uint8_t temp = values[j];
                values[j] = values[j + 1];
                values[j + 1] = temp;
            }
        }
    }

    if (count % 2 == 0) {
        return (values[count/2 - 1] + values[count/2]) / 2.0f;
    } else {
        return values[count/2];
    }
}

PrecisionColorData calculateStatisticalAverage() {
    PrecisionColorData result = {0};

    if (totalValidSamples == 0) {
        Serial.println("No valid samples for statistical processing");
        return result;
    }

    Serial.printf("Processing %d samples for statistical average...\n", totalValidSamples);

    // Extract RGB values for outlier filtering
    uint8_t rValues[MAX_SCAN_SAMPLES], gValues[MAX_SCAN_SAMPLES], bValues[MAX_SCAN_SAMPLES];
    int validCount = 0;

    for (int i = 0; i < totalValidSamples; i++) {
        if (scanSamples[i].isValid) {
            rValues[validCount] = scanSamples[i].r;
            gValues[validCount] = scanSamples[i].g;
            bValues[validCount] = scanSamples[i].b;
            validCount++;
        }
    }

    // Calculate medians for outlier detection
    float medianR = calculateMedian(rValues, validCount);
    float medianG = calculateMedian(gValues, validCount);
    float medianB = calculateMedian(bValues, validCount);

    Serial.printf("Medians: R=%.1f, G=%.1f, B=%.1f\n", medianR, medianG, medianB);

    // Filter outliers (remove samples >20% from median)
    float sumR = 0, sumG = 0, sumB = 0;
    float sumX = 0, sumY = 0, sumZ = 0;
    float sumConfidence = 0;
    int filteredCount = 0;

    for (int i = 0; i < totalValidSamples; i++) {
        if (!scanSamples[i].isValid) continue;

        float rDiff = abs(scanSamples[i].r - medianR) / medianR;
        float gDiff = abs(scanSamples[i].g - medianG) / (medianG + 1); // +1 to avoid division by zero
        float bDiff = abs(scanSamples[i].b - medianB) / (medianB + 1);

        // Include sample if all RGB components are within 20% of median
        if (rDiff <= 0.20f && gDiff <= 0.20f && bDiff <= 0.20f) {
            sumR += scanSamples[i].r;
            sumG += scanSamples[i].g;
            sumB += scanSamples[i].b;
            sumX += scanSamples[i].x;
            sumY += scanSamples[i].y;
            sumZ += scanSamples[i].z;
            sumConfidence += scanSamples[i].confidence;
            filteredCount++;
        }
    }

    if (filteredCount == 0) {
        Serial.println("All samples filtered out as outliers, using raw average");
        // Fallback to simple average if all samples are outliers
        for (int i = 0; i < totalValidSamples; i++) {
            if (scanSamples[i].isValid) {
                sumR += scanSamples[i].r;
                sumG += scanSamples[i].g;
                sumB += scanSamples[i].b;
                sumX += scanSamples[i].x;
                sumY += scanSamples[i].y;
                sumZ += scanSamples[i].z;
                sumConfidence += scanSamples[i].confidence;
                filteredCount++;
            }
        }
    }

    // Calculate averages
    result.r = (uint8_t)(sumR / filteredCount);
    result.g = (uint8_t)(sumG / filteredCount);
    result.b = (uint8_t)(sumB / filteredCount);
    result.x = (uint16_t)(sumX / filteredCount);
    result.y = (uint16_t)(sumY / filteredCount);
    result.z = (uint16_t)(sumZ / filteredCount);
    result.confidence = sumConfidence / filteredCount;

    // Calculate standard deviation for confidence scoring
    float sumSqDiffR = 0, sumSqDiffG = 0, sumSqDiffB = 0;
    for (int i = 0; i < totalValidSamples; i++) {
        if (scanSamples[i].isValid) {
            float diffR = scanSamples[i].r - result.r;
            float diffG = scanSamples[i].g - result.g;
            float diffB = scanSamples[i].b - result.b;
            sumSqDiffR += diffR * diffR;
            sumSqDiffG += diffG * diffG;
            sumSqDiffB += diffB * diffB;
        }
    }

    float stdDevR = sqrt(sumSqDiffR / filteredCount);
    float stdDevG = sqrt(sumSqDiffG / filteredCount);
    float stdDevB = sqrt(sumSqDiffB / filteredCount);
    float avgStdDev = (stdDevR + stdDevG + stdDevB) / 3.0f;

    // Convert standard deviation to confidence score (lower std dev = higher confidence)
    float consistencyScore = max(0.0f, 100.0f - (avgStdDev * 2.0f));
    result.confidence = (result.confidence + consistencyScore) / 2.0f; // Combine sensor and consistency confidence

    // Generate hex color
    sprintf(result.hexColor, "#%02X%02X%02X", result.r, result.g, result.b);

    Serial.printf("Statistical Result: RGB(%d,%d,%d) from %d/%d samples, Confidence=%.1f%%\n",
                 result.r, result.g, result.b, filteredCount, totalValidSamples, result.confidence);

    return result;
}

// ------------------------------------------------------------------------------------
// Sample Storage Management Functions
//
// **AUTOMATIC SAMPLE SAVING SYSTEM**
// - Every completed 7-second scan automatically saves to storage
// - 30-sample circular buffer with FIFO deletion when full
// - Complete Dulux paint metadata for house painting purchases
// - EEPROM persistence across device reboots
// - Enhanced visual confirmation in web UI
// ------------------------------------------------------------------------------------
void saveSamplesToEEPROM() {
    preferences.begin("colorSamples", false);

    // Save sample metadata
    preferences.putInt("totalSamples", totalSamples);
    preferences.putInt("currentIndex", currentSampleIndex);

    // Save each sample
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        String key = "sample_" + String(i);
        if (storedSamples[i].isValid) {
            preferences.putBytes(key.c_str(), &storedSamples[i], sizeof(ColorSample));
        } else {
            // Only try to remove if the key exists to avoid "NOT_FOUND" errors
            if (preferences.getBytesLength(key.c_str()) > 0) {
                preferences.remove(key.c_str());
            }
        }
    }

    preferences.end();
    Serial.printf("Saved %d samples to EEPROM\n", totalSamples);
}

void loadSamplesFromEEPROM() {
    preferences.begin("colorSamples", true);

    // Load sample metadata
    totalSamples = preferences.getInt("totalSamples", 0);
    currentSampleIndex = preferences.getInt("currentIndex", 0);

    // Initialize all samples as invalid
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        storedSamples[i].isValid = false;
    }

    // Load each sample
    int loadedCount = 0;
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        String key = "sample_" + String(i);
        size_t dataSize = preferences.getBytesLength(key.c_str());
        if (dataSize == sizeof(ColorSample)) {
            preferences.getBytes(key.c_str(), &storedSamples[i], sizeof(ColorSample));
            if (storedSamples[i].isValid) {
                loadedCount++;
            }
        }
    }

    preferences.end();
    Serial.printf("Loaded %d samples from EEPROM\n", loadedCount);
}

void addSampleToStorage(const ColorSample& sample) {
    // Add sample to circular buffer
    storedSamples[currentSampleIndex] = sample;
    storedSamples[currentSampleIndex].isValid = true;
    storedSamples[currentSampleIndex].timestamp = millis();

    // Update indices
    currentSampleIndex = (currentSampleIndex + 1) % MAX_STORED_SAMPLES;
    if (totalSamples < MAX_STORED_SAMPLES) {
        totalSamples++;
    }

    // Save to EEPROM
    saveSamplesToEEPROM();

    Serial.printf("Sample added to storage. Total samples: %d\n", totalSamples);
}

void clearAllSamples() {
    // Clear memory
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        storedSamples[i].isValid = false;
    }
    totalSamples = 0;
    currentSampleIndex = 0;
    hasFrozenSample = false;

    // Clear EEPROM
    preferences.begin("colorSamples", false);
    preferences.clear();
    preferences.end();

    Serial.println("All samples cleared from storage and EEPROM");
}



// ------------------------------------------------------------------------------------
// Settings Management Functions
// ------------------------------------------------------------------------------------
// Forward declaration
void applySensorSettings();
void saveSettings() {
    preferences.begin("colorSettings", false);
    preferences.putUChar("integrationTime", currentSettings.integrationTime);
    preferences.putUChar("alsGain", currentSettings.alsGain);
    preferences.putFloat("irCompensation", currentSettings.irCompensation);
    preferences.putUShort("srgbNorm", currentSettings.srgbNormalization);
    preferences.putBool("adaptiveScaling", currentSettings.adaptiveScaling);
    preferences.putBool("isCalibrated", currentSettings.isCalibrated);
    preferences.putBool("hasWhiteBalance", currentSettings.hasWhiteBalance);
    preferences.putBool("hasDarkCalib", currentSettings.hasDarkCalibration);
    preferences.end();
    Serial.println("Settings saved to EEPROM");
}

void loadSettings() {
    preferences.begin("colorSettings", true);

    // Load settings with defaults if not found
    currentSettings.integrationTime = preferences.getUChar("integrationTime", DEFAULT_SETTINGS.integrationTime);
    currentSettings.alsGain = preferences.getUChar("alsGain", DEFAULT_SETTINGS.alsGain);
    currentSettings.irCompensation = preferences.getFloat("irCompensation", DEFAULT_SETTINGS.irCompensation);
    currentSettings.srgbNormalization = preferences.getUShort("srgbNorm", DEFAULT_SETTINGS.srgbNormalization);
    currentSettings.adaptiveScaling = preferences.getBool("adaptiveScaling", DEFAULT_SETTINGS.adaptiveScaling);
    currentSettings.isCalibrated = preferences.getBool("isCalibrated", DEFAULT_SETTINGS.isCalibrated);
    currentSettings.hasWhiteBalance = preferences.getBool("hasWhiteBalance", DEFAULT_SETTINGS.hasWhiteBalance);
    currentSettings.hasDarkCalibration = preferences.getBool("hasDarkCalib", DEFAULT_SETTINGS.hasDarkCalibration);

    preferences.end();

    Serial.println("Settings loaded from EEPROM:");
    Serial.printf("  Integration Time: 0x%02X (~%.1fms)\n", currentSettings.integrationTime, (currentSettings.integrationTime + 1) * 2.78f);
    Serial.printf("  ALS Gain: %dx\n", (1 << (currentSettings.alsGain * 2)));
    Serial.printf("  IR Compensation: %.3f\n", currentSettings.irCompensation);
    Serial.printf("  sRGB Normalization: %d\n", currentSettings.srgbNormalization);
    Serial.printf("  Adaptive Scaling: %s\n", currentSettings.adaptiveScaling ? "Enabled" : "Disabled");
}

void resetToDefaults() {
    currentSettings = DEFAULT_SETTINGS;
    saveSettings();
    applySensorSettings();
    Serial.println("Settings reset to defaults");
}

void applySensorSettings() {
    colorSensor.setIntegrationTime(currentSettings.integrationTime);
    colorSensor.setALSGain(currentSettings.alsGain);
    Serial.printf("Applied sensor settings: Integration=0x%02X, Gain=%dx\n",
                  currentSettings.integrationTime, (1 << (currentSettings.alsGain * 2)));
}

// ------------------------------------------------------------------------------------
// Dulux Color Database (for reference display only)
// ------------------------------------------------------------------------------------
bool loadDuluxDatabase() {
    if (!LittleFS.exists("/dulux.json")) {
        Serial.println("dulux.json not found, using built-in colors");
        return false;
    }

    File file = LittleFS.open("/dulux.json", "r");
    if (!file) {
        Serial.println("Failed to open dulux.json");
        return false;
    }

    // Check file size and available memory
    size_t fileSize = file.size();
    size_t freeHeap = ESP.getFreeHeap();
    size_t freePsram = ESP.getFreePsram();

    Serial.printf("Dulux JSON file size: %d bytes\n", fileSize);
    Serial.printf("Free heap: %d bytes, Free PSRAM: %d bytes\n", freeHeap, freePsram);

    // Streaming JSON parser with 128KB memory limit
    const size_t MAX_JSON_MEMORY = 128 * 1024; // 128KB limit

    Serial.printf("Using streaming JSON parser with 128KB memory limit for %d byte file\n", fileSize);

    // Clear existing database
    COLOR_DATABASE_SIZE = 0;

    // Simple streaming parser - read line by line and parse individual color objects
    String line;
    line.reserve(512); // Reserve space for typical JSON line

    bool inArray = false;
    int colorCount = 0;

    Serial.println("Starting streaming JSON parse...");

    while (file.available() && COLOR_DATABASE_SIZE < MAX_DULUX_COLORS) {
        char c = file.read();

        if (c == '\n' || c == '\r') {
            // Process complete line
            if (line.length() > 10 && line.indexOf('{') >= 0 && line.indexOf('}') >= 0) {
                // Extract JSON object from line
                int start = line.indexOf('{');
                int end = line.lastIndexOf('}');
                if (start >= 0 && end > start) {
                    String jsonObj = line.substring(start, end + 1);

                    // Parse single color object with small document
                    JsonDocument colorDoc;
                    DeserializationError error = deserializeJson(colorDoc, jsonObj);

                    if (!error && colorDoc.is<JsonObject>()) {
                        JsonObject color = colorDoc.as<JsonObject>();

                        if (color["name"].is<const char*>() && color["r"].is<int>() &&
                            color["g"].is<int>() && color["b"].is<int>()) {

                            // Copy enhanced color data to database
                            strncpy(colorDatabase[COLOR_DATABASE_SIZE].name, color["name"], MAX_COLOR_NAME_LEN - 1);
                            colorDatabase[COLOR_DATABASE_SIZE].name[MAX_COLOR_NAME_LEN - 1] = '\0';

                            // Copy Dulux metadata with fallbacks
                            const char* codeStr = color["code"].is<const char*>() ? color["code"].as<const char*>() : "UNKNOWN";
                            strncpy(colorDatabase[COLOR_DATABASE_SIZE].code, codeStr, 15);
                            colorDatabase[COLOR_DATABASE_SIZE].code[15] = '\0';

                            const char* lrvStr = color["lrv"].is<const char*>() ? color["lrv"].as<const char*>() : "0.0";
                            strncpy(colorDatabase[COLOR_DATABASE_SIZE].lrv, lrvStr, 7);
                            colorDatabase[COLOR_DATABASE_SIZE].lrv[7] = '\0';

                            const char* idStr = color["id"].is<const char*>() ? color["id"].as<const char*>() : "000000";
                            strncpy(colorDatabase[COLOR_DATABASE_SIZE].id, idStr, 15);
                            colorDatabase[COLOR_DATABASE_SIZE].id[15] = '\0';

                            colorDatabase[COLOR_DATABASE_SIZE].r = color["r"];
                            colorDatabase[COLOR_DATABASE_SIZE].g = color["g"];
                            colorDatabase[COLOR_DATABASE_SIZE].b = color["b"];
                            COLOR_DATABASE_SIZE++;

                            colorCount++;
                            if (colorCount % 100 == 0) {
                                Serial.printf("Loaded %d colors...\n", colorCount);
                                yield(); // Prevent watchdog timeout
                            }
                        }
                    }
                }
            }
            line = ""; // Clear line buffer
        } else {
            line += c;

            // Prevent line buffer from growing too large
            if (line.length() > 1024) {
                line = "";
            }
        }

        // Yield periodically to prevent watchdog timeout
        if (colorCount % 50 == 0) {
            yield();
        }
    }

    file.close();

    Serial.printf("Streaming parse complete: loaded %d colors\n", COLOR_DATABASE_SIZE);
    Serial.printf("Final memory - Free heap: %d bytes, Free PSRAM: %d bytes\n",
                  ESP.getFreeHeap(), ESP.getFreePsram());

    return COLOR_DATABASE_SIZE > 0;
}

// ------------------------------------------------------------------------------------
// Helper Functions
// Color wheel function for smooth rainbow cycling (matching MicroPython pros3.rgb_color_wheel)
uint32_t colorWheel(byte wheelPos) {
    // Normalize to 0-255 range
    wheelPos = wheelPos % 256;

    if (wheelPos < 85) {
        // Red to Green transition
        return rgbLED.Color(255 - wheelPos * 3, wheelPos * 3, 0);
    } else if (wheelPos < 170) {
        // Green to Blue transition
        wheelPos -= 85;
        return rgbLED.Color(0, 255 - wheelPos * 3, wheelPos * 3);
    } else {
        // Blue to Red transition
        wheelPos -= 170;
        return rgbLED.Color(wheelPos * 3, 0, 255 - wheelPos * 3);
    }
}

// ------------------------------------------------------------------------------------
// RGB LED Color Enhancement Functions
// ------------------------------------------------------------------------------------

// Apply gamma correction to a single color channel (0-255)
uint8_t applyGammaCorrection(uint8_t value) {
    // Gamma correction with gamma = 2.2 for more natural colors
    float normalized = value / 255.0f;
    float corrected = pow(normalized, 1.0f / 2.2f);
    return (uint8_t)(corrected * 255.0f);
}

// Enhance color saturation
void enhanceColorSaturation(uint8_t &r, uint8_t &g, uint8_t &b, float saturationBoost = 1.5f) {
    // Convert RGB to HSV for saturation enhancement
    float rf = r / 255.0f;
    float gf = g / 255.0f;
    float bf = b / 255.0f;

    float maxVal = max(rf, max(gf, bf));
    float minVal = min(rf, min(gf, bf));
    float delta = maxVal - minVal;

    // Calculate HSV
    float h = 0, s = 0, v = maxVal;

    if (delta > 0.0001f) {
        s = delta / maxVal;

        if (maxVal == rf) {
            h = 60.0f * fmod(((gf - bf) / delta), 6.0f);
        } else if (maxVal == gf) {
            h = 60.0f * (((bf - rf) / delta) + 2.0f);
        } else {
            h = 60.0f * (((rf - gf) / delta) + 4.0f);
        }

        if (h < 0) h += 360.0f;
    }

    // Boost saturation
    s = min(1.0f, s * saturationBoost);

    // Convert back to RGB
    float c = v * s;
    float x = c * (1.0f - abs(fmod(h / 60.0f, 2.0f) - 1.0f));
    float m = v - c;

    if (h >= 0 && h < 60) {
        rf = c; gf = x; bf = 0;
    } else if (h >= 60 && h < 120) {
        rf = x; gf = c; bf = 0;
    } else if (h >= 120 && h < 180) {
        rf = 0; gf = c; bf = x;
    } else if (h >= 180 && h < 240) {
        rf = 0; gf = x; bf = c;
    } else if (h >= 240 && h < 300) {
        rf = x; gf = 0; bf = c;
    } else {
        rf = c; gf = 0; bf = x;
    }

    r = (uint8_t)((rf + m) * 255.0f);
    g = (uint8_t)((gf + m) * 255.0f);
    b = (uint8_t)((bf + m) * 255.0f);
}

// Normalize brightness to optimal LED display range
void normalizeBrightness(uint8_t &r, uint8_t &g, uint8_t &b) {
    // Find the maximum component
    uint8_t maxComponent = max(r, max(g, b));

    if (maxComponent == 0) {
        // Avoid division by zero - set to dim white
        r = g = b = 20;
        return;
    }

    // Calculate scaling factor to bring max component to optimal range (180-255)
    float scaleFactor;
    if (maxComponent < 50) {
        // Very dim colors - boost significantly
        scaleFactor = 180.0f / maxComponent;
    } else if (maxComponent < 120) {
        // Dim colors - moderate boost
        scaleFactor = 200.0f / maxComponent;
    } else {
        // Bright colors - slight boost or maintain
        scaleFactor = 255.0f / maxComponent;
    }

    // Apply scaling with clamping
    r = min(255, (int)(r * scaleFactor));
    g = min(255, (int)(g * scaleFactor));
    b = min(255, (int)(b * scaleFactor));
}

// Enhance contrast for better visual distinction
void enhanceContrast(uint8_t &r, uint8_t &g, uint8_t &b, float contrast = 1.3f) {
    // Apply contrast enhancement around midpoint (128)
    float midpoint = 128.0f;

    r = (uint8_t)min(255.0f, max(0.0f, midpoint + (r - midpoint) * contrast));
    g = (uint8_t)min(255.0f, max(0.0f, midpoint + (g - midpoint) * contrast));
    b = (uint8_t)min(255.0f, max(0.0f, midpoint + (b - midpoint) * contrast));
}

// Apply all enhancements to create vibrant LED display colors
void enhanceColorForLED(uint8_t &r, uint8_t &g, uint8_t &b) {
    // Step 1: Normalize brightness first
    normalizeBrightness(r, g, b);

    // Step 2: Enhance saturation for more vivid colors
    enhanceColorSaturation(r, g, b, 1.4f);

    // Step 3: Apply contrast enhancement
    enhanceContrast(r, g, b, 1.2f);

    // Step 4: Apply gamma correction for natural appearance
    r = applyGammaCorrection(r);
    g = applyGammaCorrection(g);
    b = applyGammaCorrection(b);
}

void updateRGBStatusLED() {
    // Update RGB LED behavior based on scanning phase
    if (millis() - lastRGBUpdate > 15) {

        switch (currentScanPhase) {
            case SCAN_PREPARATION:
                // Flash white once per second during countdown
                {
                    unsigned long phaseTime = millis() - scanPhaseStartTime;
                    unsigned long secondInPhase = phaseTime % 1000;

                    if (secondInPhase < 200) { // White for first 200ms of each second
                        rgbLED.setPixelColor(0, rgbLED.Color(255, 255, 255));
                    } else {
                        rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 0)); // Off for remainder
                    }
                    rgbLED.show();
                }
                break;

            case SCAN_ACTIVE:
                // Show enhanced measured color during active scanning
                if (sensorInitialized) {
                    uint8_t enhancedR = currentColor.r;
                    uint8_t enhancedG = currentColor.g;
                    uint8_t enhancedB = currentColor.b;

                    // Apply color enhancements for vibrant LED display
                    enhanceColorForLED(enhancedR, enhancedG, enhancedB);

                    uint32_t enhancedColor = rgbLED.Color(enhancedR, enhancedG, enhancedB);
                    rgbLED.setPixelColor(0, enhancedColor);
                    rgbLED.show();

                    // Debug output every 2 seconds during active scanning
                    static unsigned long lastScanDebug = 0;
                    if (millis() - lastScanDebug > 2000) {
                        Serial.printf("RGB LED: Original RGB(%d,%d,%d) -> Enhanced RGB(%d,%d,%d)\n",
                                     currentColor.r, currentColor.g, currentColor.b,
                                     enhancedR, enhancedG, enhancedB);
                        lastScanDebug = millis();
                    }
                }
                break;

            case SCAN_PROCESSING:
                // Pulse blue during processing
                {
                    float pulse = (sin((millis() - scanPhaseStartTime) * 0.01f) + 1.0f) / 2.0f;
                    uint8_t blueIntensity = (uint8_t)(pulse * 255);
                    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, blueIntensity));
                    rgbLED.show();
                }
                break;

            case SCAN_IDLE:
            default:
                // Rainbow cycling when idle
                {
                    uint32_t color = colorWheel(rgbColorWheel);
                    rgbLED.setPixelColor(0, color);
                    rgbLED.show();

                    // Debug output every 5 seconds for rainbow mode
                    static unsigned long lastRainbowDebug = 0;
                    if (millis() - lastRainbowDebug > 5000) {
                        Serial.printf("ProS3 RGB LED: rainbow wheel=%d, color=0x%06X\n",
                                     rgbColorWheel, color);
                        lastRainbowDebug = millis();
                    }

                    rgbColorWheel++;
                    if (rgbColorWheel >= 256) {
                        rgbColorWheel = 0;
                    }
                }
                break;
        }

        lastRGBUpdate = millis();
    }
}

void setIlluminationLED(bool state) {
    // Only update and log if state actually changes
    if (illuminationState != state) {
        illuminationState = state;
        digitalWrite(ILLUMINATION_LED_PIN, state ? HIGH : LOW);
        Serial.printf("Illumination LED set to: %s\n", state ? "ON" : "OFF");
    }
}

// Manual LED toggle function (independent of scanning)
void toggleManualLED() {
    manualLEDToggle = !manualLEDToggle;
    setIlluminationLED(manualLEDToggle);
    Serial.printf("Manual LED toggle: %s\n", manualLEDToggle ? "ON" : "OFF");
}

// Calculate measurement confidence based on sensor conditions
float calculateMeasurementConfidence() {
    float confidence = 100.0f;

    // Reduce confidence if saturated
    if (currentColor.isSaturated) {
        confidence -= 30.0f;
    }

    // Reduce confidence for very low light levels
    uint16_t maxChannel = max(currentColor.x, max(currentColor.y, currentColor.z));
    if (maxChannel < 100) {
        confidence -= 40.0f;
    } else if (maxChannel < 500) {
        confidence -= 20.0f;
    }

    // Boost confidence if calibrated
    if (currentColor.isCalibrated) {
        confidence += 10.0f;
    }

    return max(0.0f, min(100.0f, confidence));
}

// Apply advanced color processing with current settings
void processColorMeasurement() {
    // Apply IR compensation if enabled
    if (currentSettings.irCompensation > 0) {
        float irFactor = currentSettings.irCompensation;
        currentColor.x = max(0, (int)(currentColor.x - currentColor.ir1 * irFactor));
        currentColor.y = max(0, (int)(currentColor.y - currentColor.ir1 * irFactor));
        currentColor.z = max(0, (int)(currentColor.z - currentColor.ir1 * irFactor));
    }

    // Apply adaptive scaling if enabled
    if (currentSettings.adaptiveScaling) {
        uint16_t maxChannel = max(currentColor.x, max(currentColor.y, currentColor.z));
        if (maxChannel > 0 && maxChannel < currentSettings.srgbNormalization / 4) {
            // Boost low signals
            float scaleFactor = (float)currentSettings.srgbNormalization / (maxChannel * 4);
            scaleFactor = min(scaleFactor, 4.0f); // Limit boost to 4x
            currentColor.x = min(65535, (int)(currentColor.x * scaleFactor));
            currentColor.y = min(65535, (int)(currentColor.y * scaleFactor));
            currentColor.z = min(65535, (int)(currentColor.z * scaleFactor));
        }
    }
}

// Convert RGB to hex string
void rgbToHex(uint8_t r, uint8_t g, uint8_t b, char* hexStr) {
    sprintf(hexStr, "#%02X%02X%02X", r, g, b);
}

// ------------------------------------------------------------------------------------
// Sensor Functions
// ------------------------------------------------------------------------------------
bool initializeSensor() {
    Serial.println("Initializing TCS3430 precision color sensor...");

    if (!colorSensor.begin()) {
        Serial.println("ERROR: Failed to initialize TCS3430 sensor!");
        return false;
    }

    // Load settings and apply to sensor
    loadSettings();
    applySensorSettings();

    Serial.println("TCS3430 precision color sensor initialized successfully");
    return true;
}

bool readPrecisionColorSensor() {
    if (!sensorInitialized) {
        Serial.println("ERROR: Sensor not initialized");
        return false;
    }

    // Read raw XYZ and IR values
    currentColor.x = colorSensor.getXData();
    currentColor.y = colorSensor.getYData();
    currentColor.z = colorSensor.getZData();
    currentColor.ir1 = colorSensor.getIR1Data();
    currentColor.ir2 = colorSensor.getIR2Data();

    // Check for saturation
    currentColor.isSaturated = colorSensor.isSaturated();

    // Apply advanced color processing
    processColorMeasurement();

    // Get calibrated XYZ values if calibration is available
    currentColor.isCalibrated = colorSensor.getCalibratedXYZ(&currentColor.calibratedX,
                                                             &currentColor.calibratedY,
                                                             &currentColor.calibratedZ);

    if (currentColor.isCalibrated) {
        // Use proper XYZ to RGB conversion with calibrated values
        colorSensor.convertXYZtoRGB(currentColor.calibratedX, currentColor.calibratedY, currentColor.calibratedZ,
                                   &currentColor.r,
                                   &currentColor.g,
                                   &currentColor.b);
    } else {
        // Use processed raw values with normalization
        float X = (float)currentColor.x / currentSettings.srgbNormalization;
        float Y = (float)currentColor.y / currentSettings.srgbNormalization;
        float Z = (float)currentColor.z / currentSettings.srgbNormalization;

        colorSensor.convertXYZtoRGB(X, Y, Z,
                                   &currentColor.r,
                                   &currentColor.g,
                                   &currentColor.b);
    }

    // Generate hex color string
    rgbToHex(currentColor.r, currentColor.g, currentColor.b, currentColor.hexColor);

    // Calculate measurement confidence
    currentColor.confidence = calculateMeasurementConfidence();

    return true;
}

// ------------------------------------------------------------------------------------
// Web Server Functions
// ------------------------------------------------------------------------------------
void handleRoot() {
    File file = LittleFS.open("/index.html", "r");
    if (file) {
        server.streamFile(file, "text/html");
        file.close();
    } else {
        server.send(404, "text/plain", "index.html not found");
    }
}

void handleStyleCSS() {
    File file = LittleFS.open("/style.css", "r");
    if (file) {
        server.streamFile(file, "text/css");
        file.close();
    } else {
        server.send(404, "text/plain", "style.css not found");
    }
}

void handleFullData() {
    // Take a fresh sensor reading for real-time data
    if (sensorInitialized) {
        readPrecisionColorSensor();
    }

    JsonDocument doc;
    doc["data_ready"] = true;

    // Main color measurement data (for original dual-display compatibility)
    doc["measured_r"] = currentColor.r;
    doc["measured_g"] = currentColor.g;
    doc["measured_b"] = currentColor.b;
    doc["hex_color"] = currentColor.hexColor;

    // Enhanced precision measurement data
    doc["raw_x"] = currentColor.x;
    doc["raw_y"] = currentColor.y;
    doc["raw_z"] = currentColor.z;
    doc["ir1"] = currentColor.ir1;
    doc["ir2"] = currentColor.ir2;

    // For compatibility with advanced details section
    doc["avg_x"] = (float)currentColor.x;
    doc["avg_y"] = (float)currentColor.y;
    doc["avg_z"] = (float)currentColor.z;
    doc["avg_ir1"] = (float)currentColor.ir1;
    doc["avg_ir2"] = (float)currentColor.ir2;
    doc["avg_l"] = 0.0f;  // Placeholder for Lab values
    doc["avg_a"] = 0.0f;
    doc["avg_b"] = 0.0f;

    // Calibrated XYZ values (if available)
    if (currentColor.isCalibrated) {
        doc["calibrated_x"] = currentColor.calibratedX;
        doc["calibrated_y"] = currentColor.calibratedY;
        doc["calibrated_z"] = currentColor.calibratedZ;
        doc["using_calibration"] = true;
    } else {
        doc["calibrated_x"] = nullptr;
        doc["calibrated_y"] = nullptr;
        doc["calibrated_z"] = nullptr;
        doc["using_calibration"] = false;
    }

    // Measurement quality and confidence
    doc["confidence"] = currentColor.confidence;
    doc["confidence_value"] = currentColor.confidence;  // Add for web interface compatibility
    doc["confidence_text"] = currentColor.confidence >= 80 ? "Excellent" :
                            currentColor.confidence >= 60 ? "Good" :
                            currentColor.confidence >= 40 ? "Fair" : "Poor";
    doc["is_saturated"] = currentColor.isSaturated;
    doc["saturation_warning"] = currentColor.isSaturated ? "Sensor saturated - reduce gain or lighting" : "";

    // Current sensor settings for display
    doc["current_integration_time"] = currentSettings.integrationTime;
    doc["current_integration_ms"] = (currentSettings.integrationTime + 1) * 2.78f;
    doc["current_gain"] = currentSettings.alsGain;
    doc["current_gain_text"] = String((1 << (currentSettings.alsGain * 2))) + "x";
    doc["current_ir_compensation"] = currentSettings.irCompensation;
    doc["current_normalization"] = currentSettings.srgbNormalization;
    doc["current_adaptive_scaling"] = currentSettings.adaptiveScaling;

    // Calibration status
    doc["has_white_balance"] = currentSettings.hasWhiteBalance;
    doc["has_dark_calibration"] = currentSettings.hasDarkCalibration;
    doc["is_fully_calibrated"] = currentSettings.isCalibrated;

    // System status
    doc["is_scanning"] = isScanning;
    doc["led_state"] = illuminationState;
    doc["manual_led_toggle"] = manualLEDToggle;

    // Automatic scanning status
    doc["scan_phase"] = currentScanPhase;
    doc["scan_phase_name"] = currentScanPhase == SCAN_PREPARATION ? "preparation" :
                            currentScanPhase == SCAN_ACTIVE ? "scanning" :
                            currentScanPhase == SCAN_PROCESSING ? "processing" : "idle";

    if (currentScanPhase != SCAN_IDLE) {
        unsigned long phaseTime = millis() - scanPhaseStartTime;
        doc["phase_time_ms"] = phaseTime;

        if (currentScanPhase == SCAN_PREPARATION) {
            doc["preparation_countdown"] = max(0, 3 - (int)(phaseTime / 1000));
        } else if (currentScanPhase == SCAN_ACTIVE) {
            doc["scanning_countdown"] = max(0, 5 - (int)(phaseTime / 1000));
            doc["samples_collected"] = currentScanSampleIndex;
            doc["total_samples_target"] = MAX_SCAN_SAMPLES;
        }
    }

    // Dulux color matching (right side display)
    // Find closest Dulux match for current color
    float minDeltaE = 999.0f;
    int bestMatch = -1;
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        // Simple RGB distance calculation
        float deltaR = (float)currentColor.r - colorDatabase[i].r;
        float deltaG = (float)currentColor.g - colorDatabase[i].g;
        float deltaB = (float)currentColor.b - colorDatabase[i].b;
        float distance = sqrt(deltaR*deltaR + deltaG*deltaG + deltaB*deltaB);

        if (distance < minDeltaE) {
            minDeltaE = distance;
            bestMatch = i;
        }
    }

    // Matched color data (right side display)
    if (bestMatch >= 0) {
        doc["matched_name"] = colorDatabase[bestMatch].name;
        doc["matched_r"] = colorDatabase[bestMatch].r;
        doc["matched_g"] = colorDatabase[bestMatch].g;
        doc["matched_b"] = colorDatabase[bestMatch].b;
        doc["delta_e"] = minDeltaE;
        doc["confidence"] = minDeltaE <= 5.0 ? "Excellent" :
                           minDeltaE <= 10.0 ? "Good" :
                           minDeltaE <= 20.0 ? "Fair" : "Poor";
    } else {
        doc["matched_name"] = "No Match Found";
        doc["matched_r"] = currentColor.r;
        doc["matched_g"] = currentColor.g;
        doc["matched_b"] = currentColor.b;
        doc["delta_e"] = 999.0f;
        doc["confidence"] = "Poor";
    }

    // Frozen sample data (for captured sample display)
    if (hasFrozenSample) {
        doc["frozen_sample"] = true;
        doc["frozen_hex"] = frozenSample.hexColor;
        doc["frozen_r"] = frozenSample.r;
        doc["frozen_g"] = frozenSample.g;
        doc["frozen_b"] = frozenSample.b;
        doc["frozen_confidence"] = frozenSample.confidence;
    } else {
        doc["frozen_sample"] = false;
    }

    // Sample storage info
    doc["total_samples"] = totalSamples;  // Use consistent naming
    doc["total_stored_samples"] = totalSamples;  // Keep legacy field
    doc["max_samples"] = MAX_STORED_SAMPLES;

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleStartScan() {
    // Start the automatic 7-second scanning sequence
    currentScanPhase = SCAN_PREPARATION;
    scanPhaseStartTime = millis();
    lastCountdownUpdate = millis();
    preparationCountdown = 3;
    scanningCountdown = 5;

    // Initialize sample collection
    initializeScanSamples();

    isScanning = true;
    Serial.println("=== AUTOMATIC SCAN SEQUENCE STARTED ===");
    Serial.println("Phase 1: Preparation countdown (3 seconds)");
    server.send(200, "text/plain", "Auto scan started - 7 second sequence");
}

void handleStopScan() {
    // This endpoint is now used for manual interruption of automatic scan
    if (currentScanPhase != SCAN_IDLE) {
        Serial.println("=== AUTOMATIC SCAN INTERRUPTED ===");
        currentScanPhase = SCAN_IDLE;
        isScanning = false;
        server.send(200, "text/plain", "Automatic scan interrupted");
    } else {
        server.send(200, "text/plain", "No scan in progress");
    }
}

void handleToggleLED() {
    // Toggle manual LED state (independent of scanning)
    manualLEDToggle = !manualLEDToggle;

    // Apply the manual toggle state
    setIlluminationLED(manualLEDToggle);

    Serial.printf("Manual LED toggle: %s (independent of scanning)\n", manualLEDToggle ? "ON" : "OFF");

    JsonDocument doc;
    doc["led_state"] = manualLEDToggle;
    doc["manual_toggle"] = manualLEDToggle;
    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

// Add missing API endpoints that your UI expects
void handleWhiteBalance() {
    Serial.println("=== WHITE BALANCE CALIBRATION ===");
    Serial.println("Place a white reference object (white paper) under the sensor and ensure good lighting");

    // Turn on LED for calibration
    setIlluminationLED(true);
    delay(500); // Allow LED to stabilize

    // Take multiple readings for better accuracy
    uint16_t whiteX = 0, whiteY = 0, whiteZ = 0;
    int validReadings = 0;

    for (int i = 0; i < 10; i++) {
        delay(100);
        uint16_t x = colorSensor.getXData();
        uint16_t y = colorSensor.getYData();
        uint16_t z = colorSensor.getZData();

        // Only use readings that are reasonably bright (white should be bright)
        if (x > 200 && y > 200 && z > 50) {
            whiteX += x;
            whiteY += y;
            whiteZ += z;
            validReadings++;
        }

        Serial.printf("White sample %d: X=%d, Y=%d, Z=%d\n", i+1, x, y, z);
    }

    if (validReadings >= 5) {
        // Calculate average white reference
        whiteX /= validReadings;
        whiteY /= validReadings;
        whiteZ /= validReadings;

        // Perform proper white balance calibration
        bool success = colorSensor.performWhiteBalanceCalibration(10);

        if (success) {
            currentSettings.hasWhiteBalance = true;
            currentSettings.isCalibrated = currentSettings.hasWhiteBalance && currentSettings.hasDarkCalibration;
            saveSettings();

            Serial.printf("White balance calibration successful! White reference: X=%d, Y=%d, Z=%d\n",
                         whiteX, whiteY, whiteZ);
            server.send(200, "text/plain", "White balance calibration completed successfully");
        } else {
            Serial.println("Failed to perform white balance calibration");
            server.send(500, "text/plain", "White balance calibration failed - sensor error");
        }
    } else {
        Serial.printf("Insufficient valid white readings (%d/10). Object may be too dark or not detected.\n", validReadings);
        server.send(500, "text/plain", "White balance calibration failed - object too dark or not detected");
    }

    // Turn LED back to scanning state
    if (!isScanning) {
        setIlluminationLED(false);
    }

    Serial.println("=== WHITE BALANCE COMPLETED ===");
}

void handleDarkCalibration() {
    Serial.println("=== DARK CALIBRATION ===");

    // Turn off LED for dark calibration
    setIlluminationLED(false);

    // Perform dark calibration
    bool success = colorSensor.performDarkCalibration(10);

    if (success) {
        currentSettings.hasDarkCalibration = true;
        currentSettings.isCalibrated = currentSettings.hasWhiteBalance && currentSettings.hasDarkCalibration;
        saveSettings();
        server.send(200, "text/plain", "Dark calibration completed successfully");
    } else {
        server.send(500, "text/plain", "Dark calibration failed");
    }

    Serial.println("=== DARK CALIBRATION COMPLETED ===");
}

void handleBlackCalibration() {
    Serial.println("=== BLACK CALIBRATION ===");
    Serial.println("Place a black reference object (black paper/fabric) under the sensor");

    // Turn on LED for black calibration (we need some light to measure black)
    setIlluminationLED(true);
    delay(500); // Allow LED to stabilize

    // Take multiple readings for black reference
    uint16_t blackX = 0, blackY = 0, blackZ = 0;
    int validReadings = 0;

    for (int i = 0; i < 10; i++) {
        delay(100);
        uint16_t x = colorSensor.getXData();
        uint16_t y = colorSensor.getYData();
        uint16_t z = colorSensor.getZData();

        // Only use readings that are reasonably low (black should be dark)
        if (x < 1000 && y < 1000 && z < 1000) {
            blackX += x;
            blackY += y;
            blackZ += z;
            validReadings++;
        }

        Serial.printf("Black sample %d: X=%d, Y=%d, Z=%d\n", i+1, x, y, z);
    }

    if (validReadings >= 5) {
        // Calculate average black reference
        blackX /= validReadings;
        blackY /= validReadings;
        blackZ /= validReadings;

        // Use the existing dark calibration method which is similar to black calibration
        bool success = colorSensor.performDarkCalibration(validReadings);

        if (success) {
            currentSettings.hasDarkCalibration = true;
            currentSettings.isCalibrated = currentSettings.hasWhiteBalance && currentSettings.hasDarkCalibration;
            saveSettings();

            Serial.printf("Black calibration successful! Black reference: X=%d, Y=%d, Z=%d\n",
                         blackX, blackY, blackZ);
            server.send(200, "text/plain", "Black calibration completed successfully");
        } else {
            Serial.println("Failed to perform black calibration");
            server.send(500, "text/plain", "Black calibration failed - sensor error");
        }
    } else {
        Serial.printf("Insufficient valid black readings (%d/10). Object may be too bright.\n", validReadings);
        server.send(500, "text/plain", "Black calibration failed - object too bright or not detected");
    }

    // Turn LED back to scanning state
    if (!isScanning) {
        setIlluminationLED(false);
    }

    Serial.println("=== BLACK CALIBRATION COMPLETED ===");
}

void handleOptimizeGain() {
    Serial.println("=== OPTIMIZING GAIN ===");

    uint8_t optimalGain = colorSensor.getOptimalGain();
    colorSensor.setALSGain(optimalGain);

    Serial.printf("Optimal gain set to: %d\n", optimalGain);

    JsonDocument doc;
    doc["optimal_gain"] = optimalGain;
    doc["gain_names"] = JsonArray();
    doc["gain_names"].add("1x");
    doc["gain_names"].add("4x");
    doc["gain_names"].add("16x");
    doc["gain_names"].add("64x");

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleGetSettings() {
    JsonDocument doc;
    doc["integration_time"] = currentSettings.integrationTime;
    doc["integration_time_ms"] = (currentSettings.integrationTime + 1) * 2.78f;
    doc["als_gain"] = currentSettings.alsGain;
    doc["als_gain_value"] = (1 << (currentSettings.alsGain * 2));
    doc["ir_compensation"] = currentSettings.irCompensation;
    doc["srgb_normalization"] = currentSettings.srgbNormalization;
    doc["adaptive_scaling"] = currentSettings.adaptiveScaling;
    doc["is_calibrated"] = currentSettings.isCalibrated;
    doc["has_white_balance"] = currentSettings.hasWhiteBalance;
    doc["has_dark_calibration"] = currentSettings.hasDarkCalibration;

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleSetSettings() {
    if (server.hasArg("plain")) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, server.arg("plain"));

        if (error) {
            server.send(400, "text/plain", "Invalid JSON");
            return;
        }

        // Update settings from JSON (using modern ArduinoJson syntax)
        if (!doc["integration_time"].isNull()) {
            currentSettings.integrationTime = doc["integration_time"];
        }
        if (!doc["als_gain"].isNull()) {
            currentSettings.alsGain = doc["als_gain"];
        }
        if (!doc["ir_compensation"].isNull()) {
            currentSettings.irCompensation = doc["ir_compensation"];
        }
        if (!doc["srgb_normalization"].isNull()) {
            currentSettings.srgbNormalization = doc["srgb_normalization"];
        }
        if (!doc["adaptive_scaling"].isNull()) {
            currentSettings.adaptiveScaling = doc["adaptive_scaling"];
        }

        // Apply settings to sensor and save
        applySensorSettings();
        saveSettings();

        server.send(200, "text/plain", "Settings updated successfully");
    } else {
        server.send(400, "text/plain", "No data provided");
    }
}

void handleResetSettings() {
    resetToDefaults();
    server.send(200, "text/plain", "Settings reset to defaults");
}

void handleLivePreview() {
    // Ensure LED is on for live preview in settings
    bool wasLEDOn = illuminationState;
    if (!wasLEDOn) {
        setIlluminationLED(true);
        delay(100); // Allow LED to stabilize
    }

    // Take a fresh reading for live preview
    readPrecisionColorSensor();

    // Return current color reading for live preview in settings
    JsonDocument doc;
    doc["rgb_r"] = currentColor.r;
    doc["rgb_g"] = currentColor.g;
    doc["rgb_b"] = currentColor.b;
    doc["hex_color"] = currentColor.hexColor;
    doc["confidence"] = currentColor.confidence;
    doc["is_saturated"] = currentColor.isSaturated;
    doc["raw_x"] = currentColor.x;
    doc["raw_y"] = currentColor.y;
    doc["raw_z"] = currentColor.z;

    // Include current settings for reference
    doc["integration_ms"] = (currentSettings.integrationTime + 1) * 2.78f;
    doc["gain_text"] = String((1 << (currentSettings.alsGain * 2))) + "x";
    doc["ir_comp"] = currentSettings.irCompensation;
    doc["normalization"] = currentSettings.srgbNormalization;
    doc["adaptive"] = currentSettings.adaptiveScaling;

    // Restore LED state if it was off
    if (!wasLEDOn && !isScanning) {
        setIlluminationLED(false);
    }

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

// ------------------------------------------------------------------------------------
// Sample Management API Handlers
// ------------------------------------------------------------------------------------
void handleGetSamples() {
    // **ENHANCED DEBUGGING** for sample retrieval issues
    Serial.println("=== GET SAMPLES API CALLED ===");
    Serial.printf("Total samples in storage: %d\n", totalSamples);
    Serial.printf("Current sample index: %d\n", currentSampleIndex);
    Serial.printf("Max samples: %d\n", MAX_STORED_SAMPLES);

    JsonDocument doc;
    doc["total_samples"] = totalSamples;
    doc["max_samples"] = MAX_STORED_SAMPLES;

    JsonArray samples = doc["samples"].to<JsonArray>();

    // **DEBUG** - Check each sample slot
    int validSamplesFound = 0;
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        if (storedSamples[i].isValid) {
            validSamplesFound++;
            Serial.printf("Slot %d: VALID - RGB(%d,%d,%d) Sample#%d\n",
                         i, storedSamples[i].r, storedSamples[i].g, storedSamples[i].b, storedSamples[i].sampleNumber);
        }
    }
    Serial.printf("Found %d valid samples in storage\n", validSamplesFound);

    // Return samples in chronological order (newest first)
    for (int i = 0; i < totalSamples; i++) {
        int index = (currentSampleIndex - 1 - i + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
        Serial.printf("Processing sample %d: checking slot %d\n", i, index);

        if (storedSamples[index].isValid) {
            JsonObject sample = samples.add<JsonObject>();
            sample["index"] = i;
            sample["sample_number"] = storedSamples[index].sampleNumber;

            // Measured color data
            sample["r"] = storedSamples[index].r;
            sample["g"] = storedSamples[index].g;
            sample["b"] = storedSamples[index].b;
            sample["hex"] = storedSamples[index].hexColor;

            // **ENHANCED DULUX MATCH DATA** for house painting purchases
            sample["dulux_name"] = storedSamples[index].duluxName;
            sample["dulux_code"] = storedSamples[index].duluxCode;
            sample["dulux_lrv"] = storedSamples[index].duluxLRV;
            sample["dulux_id"] = storedSamples[index].duluxID;
            sample["dulux_r"] = storedSamples[index].duluxR;
            sample["dulux_g"] = storedSamples[index].duluxG;
            sample["dulux_b"] = storedSamples[index].duluxB;
            sample["delta_e"] = storedSamples[index].deltaE;

            // **MATCH QUALITY ASSESSMENT** for painters
            String matchQuality = "Poor";
            if (storedSamples[index].deltaE <= 5.0f) matchQuality = "Excellent";
            else if (storedSamples[index].deltaE <= 10.0f) matchQuality = "Good";
            else if (storedSamples[index].deltaE <= 20.0f) matchQuality = "Fair";
            sample["match_quality"] = matchQuality;
            sample["recommended_for_purchase"] = (storedSamples[index].deltaE <= 10.0f);

            // Raw sensor data
            sample["raw_x"] = storedSamples[index].rawX;
            sample["raw_y"] = storedSamples[index].rawY;
            sample["raw_z"] = storedSamples[index].rawZ;
            sample["confidence"] = storedSamples[index].confidence;
            sample["timestamp"] = storedSamples[index].timestamp;
        }
    }

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleClearSamples() {
    clearAllSamples();
    server.send(200, "text/plain", "All samples cleared");
}

void handleDeleteSample() {
    if (server.hasArg("index")) {
        int sampleIndex = server.arg("index").toInt();
        if (sampleIndex >= 0 && sampleIndex < totalSamples) {
            // Mark sample as invalid (simple deletion)
            int actualIndex = (currentSampleIndex - 1 - sampleIndex + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
            storedSamples[actualIndex].isValid = false;

            // Compact the array by shifting samples
            for (int i = sampleIndex; i < totalSamples - 1; i++) {
                int fromIndex = (currentSampleIndex - 1 - i - 1 + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
                int toIndex = (currentSampleIndex - 1 - i + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
                storedSamples[toIndex] = storedSamples[fromIndex];
            }

            totalSamples--;
            if (totalSamples > 0) {
                currentSampleIndex = (currentSampleIndex - 1 + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
            } else {
                currentSampleIndex = 0;
            }

            saveSamplesToEEPROM();
            server.send(200, "text/plain", "Sample deleted");
        } else {
            server.send(400, "text/plain", "Invalid sample index");
        }
    } else {
        server.send(400, "text/plain", "No sample index provided");
    }
}

void setupWebServer() {
    // Initialize LittleFS
    if (!LittleFS.begin(true)) {
        Serial.println("An Error has occurred while mounting LittleFS");
        return;
    }
    Serial.println("LittleFS mounted successfully");

    // List files in LittleFS for debugging
    Serial.println("Files in LittleFS:");
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    while (file) {
        Serial.print("  ");
        Serial.println(file.name());
        file = root.openNextFile();
    }
    root.close();

    // Load Dulux color database
    Serial.println("Starting web server first, then loading color database...");

    // Start with fallback colors to ensure web server starts quickly
    Serial.println("Using fallback color database initially");
    COLOR_DATABASE_SIZE = FALLBACK_COLOR_COUNT;
    for (int i = 0; i < FALLBACK_COLOR_COUNT; i++) {
        strncpy(colorDatabase[i].name, fallbackColors[i].name, MAX_COLOR_NAME_LEN - 1);
        colorDatabase[i].name[MAX_COLOR_NAME_LEN - 1] = '\0';
        strncpy(colorDatabase[i].code, fallbackColors[i].code, 15);
        colorDatabase[i].code[15] = '\0';
        strncpy(colorDatabase[i].lrv, fallbackColors[i].lrv, 7);
        colorDatabase[i].lrv[7] = '\0';
        strncpy(colorDatabase[i].id, fallbackColors[i].id, 15);
        colorDatabase[i].id[15] = '\0';
        colorDatabase[i].r = fallbackColors[i].r;
        colorDatabase[i].g = fallbackColors[i].g;
        colorDatabase[i].b = fallbackColors[i].b;
    }

    // Setup web server routes
    server.on("/", handleRoot);
    server.on("/style.css", handleStyleCSS);

    // Main data endpoint (enhanced with precision measurement data)
    server.on("/fulldata", handleFullData);

    // Settings API for live preview and configuration
    server.on("/live_preview", handleLivePreview);
    server.on("/get_settings", HTTP_GET, handleGetSettings);
    server.on("/set_settings", HTTP_POST, handleSetSettings);
    server.on("/reset_settings", HTTP_POST, handleResetSettings);

    // Scanning and LED control
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLED);

    // Sample management
    server.on("/get_samples", HTTP_GET, handleGetSamples);
    server.on("/clear_samples", HTTP_POST, handleClearSamples);
    server.on("/delete_sample", HTTP_POST, handleDeleteSample);

    // Calibration functions
    server.on("/white_balance", HTTP_POST, handleWhiteBalance);
    server.on("/dark_calibration", HTTP_POST, handleDarkCalibration);
    server.on("/black_calibration", HTTP_POST, handleBlackCalibration);
    server.on("/optimize_gain", HTTP_POST, handleOptimizeGain);

    // Start server
    server.begin();
    Serial.println("Web server started");
    Serial.print("Open http://");
    if (WiFi.getMode() == WIFI_AP) {
        Serial.print(WiFi.softAPIP());
    } else {
        Serial.print(WiFi.localIP());
    }
    Serial.println(" in your browser");

    // Now load the full color database in background
    Serial.println("Loading full Dulux color database in background...");
    if (loadDuluxDatabase()) {
        Serial.printf("Full color database loaded: %d colors\n", COLOR_DATABASE_SIZE);
    } else {
        Serial.println("Continuing with fallback color database");
    }
}

// ------------------------------------------------------------------------------------
// WiFi Functions
// ------------------------------------------------------------------------------------
void initializeWiFi() {
    Serial.println("============================================================");
    Serial.println("WiFi Initialization - Enhanced Diagnostics");
    Serial.println("============================================================");
    Serial.printf("SSID: %s\n", WIFI_SSID);
    Serial.printf("Password: %s\n", WIFI_PASSWORD);
    Serial.printf("Target Static IP: %s\n", STATIC_IP.toString().c_str());
    Serial.printf("Gateway: %s\n", GATEWAY.toString().c_str());
    Serial.printf("Subnet: %s\n", SUBNET.toString().c_str());

    // **ENHANCED WIFI DIAGNOSTICS** - Check available networks first
    Serial.println("Scanning for available networks...");
    WiFi.mode(WIFI_STA);
    int networkCount = WiFi.scanNetworks();
    bool targetNetworkFound = false;

    if (networkCount > 0) {
        Serial.printf("Found %d networks:\n", networkCount);
        for (int i = 0; i < networkCount; i++) {
            String ssid = WiFi.SSID(i);
            int32_t rssi = WiFi.RSSI(i);
            wifi_auth_mode_t authMode = WiFi.encryptionType(i);

            Serial.printf("  %d: %s (RSSI: %d, Auth: %d)\n", i, ssid.c_str(), rssi, authMode);

            if (ssid == WIFI_SSID) {
                targetNetworkFound = true;
                Serial.printf("  *** TARGET NETWORK FOUND! Signal: %d dBm, Auth: %d ***\n", rssi, authMode);
            }
        }
    } else {
        Serial.println("No networks found!");
    }

    if (!targetNetworkFound) {
        Serial.printf("WARNING: Target network '%s' not found in scan!\n", WIFI_SSID);
    }

    // Disconnect any previous connection
    WiFi.disconnect(true);
    delay(1000);

    // **ATTEMPT 1: DHCP Connection**
    Serial.println("\n--- ATTEMPT 1: DHCP Connection ---");
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 30) {
        delay(500);
        Serial.print(".");
        attempts++;

        // Enhanced status reporting
        if (attempts % 5 == 0) {
            Serial.println();
            wl_status_t status = WiFi.status();
            Serial.printf("WiFi Status: %d (", status);
            switch(status) {
                case WL_IDLE_STATUS: Serial.print("IDLE"); break;
                case WL_NO_SSID_AVAIL: Serial.print("NO_SSID_AVAIL"); break;
                case WL_SCAN_COMPLETED: Serial.print("SCAN_COMPLETED"); break;
                case WL_CONNECTED: Serial.print("CONNECTED"); break;
                case WL_CONNECT_FAILED: Serial.print("CONNECT_FAILED"); break;
                case WL_CONNECTION_LOST: Serial.print("CONNECTION_LOST"); break;
                case WL_DISCONNECTED: Serial.print("DISCONNECTED"); break;
                default: Serial.print("UNKNOWN"); break;
            }
            Serial.printf(") - attempt %d/30\n", attempts);
        }
    }

    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        Serial.println("\n✅ WiFi connected with DHCP!");
        Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("Gateway: %s\n", WiFi.gatewayIP().toString().c_str());
        Serial.printf("Subnet: %s\n", WiFi.subnetMask().toString().c_str());
        Serial.printf("DNS: %s\n", WiFi.dnsIP().toString().c_str());
        Serial.printf("Signal strength: %d dBm\n", WiFi.RSSI());

        // Try to set static IP (optional)
        Serial.println("Attempting to set static IP...");
        if (WiFi.config(STATIC_IP, GATEWAY, SUBNET)) {
            Serial.printf("✅ Static IP set successfully: %s\n", WiFi.localIP().toString().c_str());
        } else {
            Serial.println("⚠️ Failed to set static IP, using DHCP IP");
        }
    } else {
        Serial.println("\n❌ DHCP connection failed");

        // **ATTEMPT 2: Static IP Connection**
        Serial.println("\n--- ATTEMPT 2: Static IP Connection ---");
        WiFi.disconnect(true);
        delay(1000);

        if (WiFi.config(STATIC_IP, GATEWAY, SUBNET)) {
            WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

            attempts = 0;
            while (WiFi.status() != WL_CONNECTED && attempts < 20) {
                delay(500);
                Serial.print(".");
                attempts++;

                if (attempts % 5 == 0) {
                    Serial.printf("\nStatic IP attempt %d/20, Status: %d\n", attempts, WiFi.status());
                }
            }

            if (WiFi.status() == WL_CONNECTED) {
                wifiConnected = true;
                Serial.println("\n✅ WiFi connected with static IP!");
                Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
            }
        }
    }

    // **FALLBACK: Access Point Mode**
    if (!wifiConnected) {
        Serial.println("\n❌ All WiFi connection attempts failed");
        Serial.printf("Final WiFi Status: %d\n", WiFi.status());
        Serial.println("\n🔄 Starting Access Point mode as fallback...");

        WiFi.mode(WIFI_AP);
        bool apStarted = WiFi.softAP("ESP32-ColorSensor", "colormatching");

        if (apStarted) {
            IPAddress apIP = WiFi.softAPIP();
            Serial.println("✅ Access Point started successfully!");
            Serial.printf("Network Name: ESP32-ColorSensor\n");
            Serial.printf("Password: colormatching\n");
            Serial.printf("AP IP address: %s\n", apIP.toString().c_str());
            Serial.println("Connect your device to this network and go to http://***********");
        } else {
            Serial.println("❌ Failed to start Access Point!");
        }
        wifiConnected = false;
    }
    Serial.println("============================================================");
}

// ------------------------------------------------------------------------------------
// Main Functions
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("============================================================");
    Serial.println("ESP32-S3 ProS3 Precision Color Sensor");
    Serial.println("Advanced Color Measurement & Analysis System");
    Serial.printf("Board: ESP32-S3 ProS3 (16MB Flash, 8MB PSRAM)\n");
    Serial.printf("Free Heap: %d bytes, Free PSRAM: %d bytes\n", ESP.getFreeHeap(), ESP.getFreePsram());
    Serial.println("============================================================");

    // Allocate color database in PSRAM to avoid heap memory issues
    Serial.println("Allocating color database in PSRAM...");
    colorDatabase = (ReferenceColor*)ps_malloc(MAX_DULUX_COLORS * sizeof(ReferenceColor));
    if (colorDatabase == nullptr) {
        Serial.println("ERROR: Failed to allocate color database in PSRAM!");
        Serial.println("Using smaller fallback database in heap memory");
        // Fallback to smaller heap allocation
        colorDatabase = (ReferenceColor*)malloc(FALLBACK_COLOR_COUNT * sizeof(ReferenceColor));
        if (colorDatabase == nullptr) {
            Serial.println("CRITICAL ERROR: Cannot allocate any memory for color database!");
            while(1); // Halt system
        }
        Serial.printf("Fallback color database allocated in heap: %d bytes\n", FALLBACK_COLOR_COUNT * sizeof(ReferenceColor));
    } else {
        Serial.printf("Color database allocated in PSRAM: %d bytes\n", MAX_DULUX_COLORS * sizeof(ReferenceColor));
    }
    Serial.printf("Free Heap after allocation: %d bytes, Free PSRAM: %d bytes\n", ESP.getFreeHeap(), ESP.getFreePsram());
    
    // Initialize ProS3 onboard RGB LED
    Serial.printf("Initializing ProS3 RGB LED on GPIO%d...\n", RGB_LED_PIN);

    // Enable power to RGB LED (ProS3 specific - LDO2 power control)
    pinMode(RGB_POWER_PIN, OUTPUT);
    digitalWrite(RGB_POWER_PIN, HIGH);
    Serial.println("RGB LED power enabled (LDO2)");
    delay(50); // Allow power to stabilize

    // Initialize NeoPixel with correct format for ProS3
    rgbLED.begin();
    rgbLED.setBrightness(77); // Set to 30% brightness (0.3 like MicroPython example: 255 * 0.3 = 77)
    rgbLED.clear();
    rgbLED.show();
    delay(100);

    // Test RGB LED with color sequence to verify it's working
    Serial.println("Testing RGB LED colors...");
    rgbLED.setPixelColor(0, rgbLED.Color(255, 0, 0)); // Red
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 255, 0)); // Green
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 255)); // Blue
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 0)); // Off
    rgbLED.show();
    Serial.println("ProS3 RGB LED initialized with power control and tested successfully");

    // Initialize GPIO pins
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);

    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    
    // Initialize I2C
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(400000); // 400kHz
    Serial.println("I2C initialized");
    
    // Initialize color sensor
    sensorInitialized = initializeSensor();
    if (!sensorInitialized) {
        Serial.println("CRITICAL: Sensor initialization failed!");
        Serial.println("Check I2C connections and sensor power");
    }

    // Load saved samples from EEPROM
    loadSamplesFromEEPROM();
    
    // Initialize WiFi
    initializeWiFi();

    // Initialize Web Server (always start, even without WiFi for AP mode or later connection)
    setupWebServer();

    // Initialize illumination LED to OFF state
    setIlluminationLED(false);
    illuminationState = false;

    Serial.println("============================================================");
    Serial.println("System initialization complete!");
    Serial.println("Illumination LED: OFF (will turn on during scanning/calibration)");
    Serial.println("Color readings: PAUSED (press scan button to start)");
    Serial.println("============================================================");
}

// ------------------------------------------------------------------------------------
// Automatic Scanning State Machine
// ------------------------------------------------------------------------------------
void handleAutomaticScanningStateMachine() {
    unsigned long currentTime = millis();
    unsigned long phaseTime = currentTime - scanPhaseStartTime;

    switch (currentScanPhase) {
        case SCAN_PREPARATION:
            // Phase 1: 3-second preparation countdown
            if (currentTime - lastCountdownUpdate >= 1000) {
                preparationCountdown--;
                lastCountdownUpdate = currentTime;
                Serial.printf("Preparation countdown: %d\n", preparationCountdown);
            }

            if (phaseTime >= 3000) {
                // Move to active scanning phase
                currentScanPhase = SCAN_ACTIVE;
                scanPhaseStartTime = currentTime;
                lastSampleTime = currentTime;
                scanningCountdown = 5;
                Serial.println("Phase 2: Active scanning (5 seconds)");
            }
            break;

        case SCAN_ACTIVE:
            // Phase 2: 5-second active scanning with sample collection

            // Update countdown display
            if (currentTime - lastCountdownUpdate >= 1000) {
                scanningCountdown--;
                lastCountdownUpdate = currentTime;
                Serial.printf("Scanning... %d seconds remaining\n", scanningCountdown);
            }

            // Collect samples every 200ms (25 samples over 5 seconds)
            if (sensorInitialized && currentTime - lastSampleTime >= 200) {
                lastSampleTime = currentTime;

                if (readPrecisionColorSensor()) {
                    // Add sample to collection for statistical processing
                    addScanSample(currentColor);
                }
            }

            if (phaseTime >= 5000) {
                // Move to processing phase
                currentScanPhase = SCAN_PROCESSING;
                scanPhaseStartTime = currentTime;
                Serial.println("Phase 3: Processing and saving...");
            }
            break;

        case SCAN_PROCESSING:
            // Phase 3: Process collected samples and save result
            if (phaseTime >= 500) { // Brief processing delay for visual feedback
                processAndSaveStatisticalResult();

                // Return to idle state
                currentScanPhase = SCAN_IDLE;
                isScanning = false;
                Serial.println("=== AUTOMATIC SCAN SEQUENCE COMPLETED ===");
            }
            break;

        case SCAN_IDLE:
        default:
            // No automatic scanning in progress
            break;
    }
}

void processAndSaveStatisticalResult() {
    Serial.println("Processing statistical average from collected samples...");

    // Calculate statistical average with outlier filtering
    PrecisionColorData averagedColor = calculateStatisticalAverage();

    if (averagedColor.r == 0 && averagedColor.g == 0 && averagedColor.b == 0) {
        Serial.println("ERROR: No valid samples collected during scan");
        return;
    }

    // Create sample from averaged data
    ColorSample newSample;
    newSample.r = averagedColor.r;
    newSample.g = averagedColor.g;
    newSample.b = averagedColor.b;
    strncpy(newSample.hexColor, averagedColor.hexColor, sizeof(newSample.hexColor));

    // Find closest Dulux match for the averaged sample
    float minDeltaE = 999.0f;
    int bestMatch = -1;
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        // Simple RGB distance calculation
        float deltaR = (float)averagedColor.r - colorDatabase[i].r;
        float deltaG = (float)averagedColor.g - colorDatabase[i].g;
        float deltaB = (float)averagedColor.b - colorDatabase[i].b;
        float distance = sqrt(deltaR*deltaR + deltaG*deltaG + deltaB*deltaB);

        if (distance < minDeltaE) {
            minDeltaE = distance;
            bestMatch = i;
        }
    }

    // Store enhanced Dulux match info
    if (bestMatch >= 0) {
        strncpy(newSample.duluxName, colorDatabase[bestMatch].name, sizeof(newSample.duluxName));
        strncpy(newSample.duluxCode, colorDatabase[bestMatch].code, sizeof(newSample.duluxCode));
        strncpy(newSample.duluxLRV, colorDatabase[bestMatch].lrv, sizeof(newSample.duluxLRV));
        strncpy(newSample.duluxID, colorDatabase[bestMatch].id, sizeof(newSample.duluxID));
        newSample.duluxR = colorDatabase[bestMatch].r;
        newSample.duluxG = colorDatabase[bestMatch].g;
        newSample.duluxB = colorDatabase[bestMatch].b;
        newSample.deltaE = minDeltaE;
    } else {
        strncpy(newSample.duluxName, "No Match", sizeof(newSample.duluxName));
        strncpy(newSample.duluxCode, "UNKNOWN", sizeof(newSample.duluxCode));
        strncpy(newSample.duluxLRV, "0.0", sizeof(newSample.duluxLRV));
        strncpy(newSample.duluxID, "000000", sizeof(newSample.duluxID));
        newSample.duluxR = averagedColor.r;
        newSample.duluxG = averagedColor.g;
        newSample.duluxB = averagedColor.b;
        newSample.deltaE = 999.0f;
    }

    // Store raw sensor data (averaged)
    newSample.rawX = averagedColor.x;
    newSample.rawY = averagedColor.y;
    newSample.rawZ = averagedColor.z;
    newSample.rawIR1 = 0; // IR values not averaged
    newSample.rawIR2 = 0;
    newSample.confidence = averagedColor.confidence;

    // Add sample metadata
    newSample.sampleNumber = totalSamples + 1; // Sequential sample number

    // Add to storage (automatically handles circular buffer and EEPROM)
    addSampleToStorage(newSample);

    // Set as frozen sample for display
    frozenSample = newSample;
    hasFrozenSample = true;

    // Update current color for display
    currentColor.r = averagedColor.r;
    currentColor.g = averagedColor.g;
    currentColor.b = averagedColor.b;
    currentColor.confidence = averagedColor.confidence;
    strncpy(currentColor.hexColor, averagedColor.hexColor, sizeof(currentColor.hexColor));

    // **ENHANCED LOGGING** for automatic sample saving
    Serial.println("=== AUTOMATIC SAMPLE SAVED ===");
    Serial.printf("Sample #%d automatically saved to storage\n", newSample.sampleNumber);
    Serial.printf("Color: RGB(%d,%d,%d) %s\n", newSample.r, newSample.g, newSample.b, newSample.hexColor);
    Serial.printf("Dulux Match: %s (Code: %s, LRV: %s)\n", newSample.duluxName, newSample.duluxCode, newSample.duluxLRV);
    Serial.printf("Confidence: %.1f%%, Delta E: %.1f\n", newSample.confidence, newSample.deltaE);
    Serial.printf("Total samples in storage: %d/%d\n", totalSamples, MAX_STORED_SAMPLES);
    Serial.println("=== SCAN SEQUENCE COMPLETED ===");
}

void loop() {
    // Update RGB LED to show system is alive with color cycling
    updateRGBStatusLED();

    // Handle automatic scanning state machine
    handleAutomaticScanningStateMachine();

    // LED control - respect manual toggle state or automatic scanning control
    if (currentScanPhase == SCAN_ACTIVE || currentScanPhase == SCAN_PREPARATION) {
        setIlluminationLED(true);  // Always on during scanning phases
    } else {
        setIlluminationLED(manualLEDToggle);  // Use manual toggle state when idle
    }

    // Read color sensor at specified interval - always read but only print when scanning
    if (sensorInitialized && millis() - lastReading >= READING_INTERVAL_MS) {
        lastReading = millis();

        if (readPrecisionColorSensor()) {
            // Only print detailed color data when actively scanning
            if (isScanning) {
                Serial.println("=== PRECISION COLOR MEASUREMENT ===");
                Serial.printf("Raw XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
                if (currentColor.isCalibrated) {
                    Serial.printf("Calibrated XYZ: X=%.3f, Y=%.3f, Z=%.3f\n",
                                 currentColor.calibratedX, currentColor.calibratedY, currentColor.calibratedZ);
                }
                Serial.printf("RGB: R=%d, G=%d, B=%d\n", currentColor.r, currentColor.g, currentColor.b);
                Serial.printf("Hex: %s\n", currentColor.hexColor);
                Serial.printf("IR: IR1=%d, IR2=%d\n", currentColor.ir1, currentColor.ir2);
                Serial.printf("Confidence: %.1f%%\n", currentColor.confidence);
                Serial.printf("Saturated: %s\n", currentColor.isSaturated ? "YES" : "NO");
                Serial.printf("Settings: Gain=%dx, Integration=%.1fms\n",
                             (1 << (currentSettings.alsGain * 2)),
                             (currentSettings.integrationTime + 1) * 2.78f);
                Serial.println("====================================");
            }
        }
    }

    // Handle web server requests (always handle, even without WiFi)
    server.handleClient();

    // Memory monitoring (every 30 seconds)
    static unsigned long lastMemoryCheck = 0;
    if (millis() - lastMemoryCheck > 30000) {
        Serial.printf("Memory Status - Free Heap: %d bytes, PSRAM: %d bytes\n",
                     ESP.getFreeHeap(), ESP.getFreePsram());
        lastMemoryCheck = millis();
    }

    // Small delay to prevent overwhelming the system
    delay(10);
}
